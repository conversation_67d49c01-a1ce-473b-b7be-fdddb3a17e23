# -*- coding: utf-8 -*-
"""
Dashboard Services Module

This module contains service classes for dashboard-related functionality,
including statistics, analytics, and summary data.
"""
import logging
from datetime import datetime, timedelta
from django.db.models import Count, Sum, Avg, Q, F
from django.utils import timezone
from django.conf import settings
from project.models import TcdAppProject, TcdAppProjectConsult, TcdAppProjectSector, TcdAppProjectSkill, TcdAppProjectService
from authentication.models import TcdAppMember, TcdUserConsult, TcdAppLogs
from search.models import TcdSector, TcdSkill, TcdService, TcdAppNotification
from chat.models import TcdChat
from utils.response import service_success_response, service_error_response
from .serializers import ProjectStatsSerializer, ConsultantTypeSerializer, TcdDashboardCategorySerializer, TcdDashboardSerializer
from MCDC.serializers import TcdNewsSerializer
from MCDC.models import TcdNews, TcdPersonalGeneralData, TcdCorporateGeneralData, TcdNoProfitGeneralData, TcdProject
from dashboard.models import TcdDashboardCategory, TcdDashboard

logger = logging.getLogger(__name__)


class DashboardStatsService:
    """
    Service class for dashboard statistics and analytics
    """

    @staticmethod
    def get_overview_stats(language='th', project_size=3, news_size=3):
        """
        Get overview statistics for the dashboard
        
        Args:
            language (str): Language preference
            
        Returns:
            dict: Service response with overview statistics
        """
        try:
            # Summary
            total_projects = TcdProject.objects.filter(is_active=1).count()
            
            # Personal general data count
            personal_count = TcdPersonalGeneralData.objects.filter(
                register_type=1,
                register_expire_date__isnull=False
            ).filter(
                Q(register_no__isnull=False) & ~Q(register_no='')
            ).count()
            
            # Corporate general data count
            corporate_count = TcdCorporateGeneralData.objects.filter(
                register_type=1,
                register_expire_date__isnull=False
            ).filter(
                Q(register_no__isnull=False) & ~Q(register_no='')
            ).count()
            
            # No profit general data count
            no_profit_count = TcdNoProfitGeneralData.objects.filter(
                register_type=1,
                register_expire_date__isnull=False
            ).filter(
                Q(register_no__isnull=False) & ~Q(register_no='')
            ).count()
            
            # Sum all three counts
            total_consultants = personal_count + corporate_count + no_profit_count
            
            total_consultants_personal = TcdPersonalGeneralData.objects.filter(
                register_type__in=[3, 4]
            ).count()

            # Project views statistics
            projects_data = TcdAppProject.objects.filter(status='1').order_by('-id')[:project_size]

            # News statistics
            news_data = TcdNews.objects.filter(status='1').order_by('-id')[:news_size]
            
            # Independent level statistics (ระดับ 1, ระดับ 2, ระดับ 3)
            independent_level_stats = TcdPersonalGeneralData.objects.filter(
                register_type=1,
                register_expire_date__isnull=False
            ).filter(
                Q(register_no__isnull=False) & ~Q(register_no='')
            ).values('rating').annotate(
                count=Count('id')
            ).order_by('rating')
            
            # Initialize independent level data
            independent_level = {
                '1': 0,
                '2': 0,
                '3': 0,
                'total_count': 0
            }
            
            # Populate the data from query results
            for stat in independent_level_stats:
                rating = stat['rating']
                count = stat['count']
                if rating in ['1', '2', '3']:
                    independent_level[rating] = count
                    independent_level['total_count'] += count
            
            # Corporate level statistics (ระดับ 1, ระดับ 2, ระดับ 3) - combining corporate and no-profit
            corporate_level_stats = TcdCorporateGeneralData.objects.filter(
                register_type=1,
                register_expire_date__isnull=False
            ).filter(
                Q(register_no__isnull=False) & ~Q(register_no='')
            ).values('rating').annotate(
                count=Count('id')
            ).order_by('rating')
            
            no_profit_level_stats = TcdNoProfitGeneralData.objects.filter(
                register_type=1,
                register_expire_date__isnull=False
            ).filter(
                Q(register_no__isnull=False) & ~Q(register_no='')
            ).values('rating').annotate(
                count=Count('id')
            ).order_by('rating')
            
            # Initialize corporate level data
            corporate_level = {
                '1': 0,
                '2': 0,
                '3': 0,
                'total_count': 0
            }
            
            # Combine counts from both corporate and no-profit data
            all_corporate_stats = list(corporate_level_stats) + list(no_profit_level_stats)
            
            # Populate the data from combined query results
            for stat in all_corporate_stats:
                rating = stat['rating']
                count = stat['count']
                if rating in ['1', '2', '3']:
                    corporate_level[rating] += count
                    corporate_level['total_count'] += count
            
            chart_data = {
                'consultant_type': {
                    'independent_count': personal_count,
                    'corporate_count': corporate_count + no_profit_count,
                    'total_count': total_consultants,
                },
                'independent_level': independent_level,
                'corporate_level': corporate_level,
            }
            
            dashboard_info = TcdDashboard.objects.filter(status=True, is_application=True).order_by('-id')[:3]
            base_file_url = getattr(settings, 'BASE_FILE_URL', '')
            dashboard_sub_dir = getattr(settings, 'DASHBOARD_SUB_DIR', '')
            dashboard_file_url = f"{base_file_url}{dashboard_sub_dir}"
            for dashboard in dashboard_info:
                dashboard.thumbnail = f"{dashboard_file_url}{dashboard.thumbnail}" if dashboard.thumbnail else ''
            
            data = {
                'overview': {
                    'total_projects': total_projects,
                    'total_consultants': total_consultants,
                    'total_members': total_consultants_personal,
                },
                'chart_data': chart_data,
                'service_info': TcdDashboardSerializer(dashboard_info, many=True).data,
                'project_stats': ProjectStatsSerializer(projects_data, many=True).data,
                'news': TcdNewsSerializer(news_data, many=True).data
            }
            
            return service_success_response(data, language)
            
        except Exception as e:
            logger.error(f"Error getting overview stats: {str(e)}")
            return service_error_response(5000, language)

    @staticmethod
    def get_project_analytics(period='30', language='th'):
        """
        Get project analytics for specified period
        
        Args:
            period (str): Period in days ('7', '30', '90', '365')
            language (str): Language preference
            
        Returns:
            dict: Service response with project analytics
        """
        try:
            # Calculate date range
            now = timezone.now()
            days = int(period)
            start_date = now - timedelta(days=days)
            
            # Project creation trend
            projects_by_date = TcdAppProject.objects.filter(
                create_date__gte=start_date
            ).extra(
                select={'date': "DATE(create_date)"}
            ).values('date').annotate(
                count=Count('id')
            ).order_by('date')
            
            # Project status distribution
            status_distribution = TcdAppProject.objects.values('status').annotate(
                count=Count('id')
            ).order_by('status')
            
            # Most viewed projects
            top_viewed_projects = TcdAppProject.objects.filter(
                create_date__gte=start_date
            ).order_by('-view')[:10]
            
            top_projects = []
            for project in top_viewed_projects:
                top_projects.append({
                    'project_id': project.id,
                    'project_name': project.name,
                    'organization': project.app_member.name if project.app_member else '',
                    'view_count': project.view,
                    'create_date': project.create_date.isoformat()
                })
            
            # Projects by sector
            sector_distribution = TcdAppProjectSector.objects.filter(
                app_project_id__in=TcdAppProject.objects.filter(
                    create_date__gte=start_date
                ).values_list('id', flat=True)
            ).values('sector_id').annotate(
                count=Count('app_project_id')
            ).order_by('-count')[:10]
            
            sector_stats = []
            for sector_stat in sector_distribution:
                sector = TcdSector.objects.filter(id=sector_stat['sector_id']).first()
                if sector:
                    sector_stats.append({
                        'sector_id': sector.id,
                        'sector_name': sector.name_th if language == 'th' else sector.name_en,
                        'project_count': sector_stat['count']
                    })
            
            data = {
                'period_days': days,
                'date_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': now.isoformat()
                },
                'project_creation_trend': list(projects_by_date),
                'status_distribution': list(status_distribution),
                'top_viewed_projects': top_projects,
                'sector_distribution': sector_stats
            }
            
            return service_success_response(data, language)
            
        except Exception as e:
            logger.error(f"Error getting project analytics: {str(e)}")
            return service_error_response(5000, language)

    @staticmethod
    def get_consultant_analytics(language='th'):
        """
        Get consultant analytics and statistics
        
        Args:
            language (str): Language preference
            
        Returns:
            dict: Service response with consultant analytics
        """
        try:
            # Consultant engagement statistics
            consultant_stats = TcdAppProjectConsult.objects.values('user_consult_id').annotate(
                project_count=Count('app_project_id'),
                avg_matching=Avg('matching'),
                total_views=Sum('consult_view')
            ).order_by('-project_count')[:10]
            
            top_consultants = []
            for stat in consultant_stats:
                consultant = TcdUserConsult.objects.filter(id=stat['user_consult_id']).first()
                if consultant:
                    top_consultants.append({
                        'consultant_id': consultant.id,
                        'consultant_username': consultant.username,
                        'project_count': stat['project_count'],
                        'average_matching': round(stat['avg_matching'] or 0, 2),
                        'total_views': stat['total_views'] or 0
                    })
            
            # Matching score distribution
            matching_ranges = [
                (0, 25, 'Low (0-25%)'),
                (25, 50, 'Medium (25-50%)'),
                (50, 75, 'High (50-75%)'),
                (75, 100, 'Very High (75-100%)')
            ]
            
            matching_distribution = []
            for min_score, max_score, label in matching_ranges:
                count = TcdAppProjectConsult.objects.filter(
                    matching__gte=min_score,
                    matching__lt=max_score if max_score < 100 else 101
                ).count()
                
                matching_distribution.append({
                    'range': label,
                    'count': count,
                    'min_score': min_score,
                    'max_score': max_score
                })
            
            # Consultant activity by type
            consultant_types = TcdUserConsult.objects.values('consult_type').annotate(
                count=Count('id')
            ).order_by('consult_type')
            
            # Recent consultant registrations (using current count since create_date field doesn't exist)
            recent_consultants = 0  # TODO: Add create_date field to TcdUserConsult model if needed
            
            data = {
                'top_consultants': top_consultants,
                'matching_distribution': matching_distribution,
                'consultant_types': list(consultant_types),
                'recent_registrations': recent_consultants,
                'total_consultants': TcdUserConsult.objects.count(),
                'active_consultants': TcdUserConsult.objects.filter(is_active_matching=True).count()
            }
            
            return service_success_response(data, language)
            
        except Exception as e:
            logger.error(f"Error getting consultant analytics: {str(e)}")
            return service_error_response(5000, language)

    @staticmethod
    def get_member_analytics(language='th'):
        """
        Get member analytics and statistics
        
        Args:
            language (str): Language preference
            
        Returns:
            dict: Service response with member analytics
        """
        try:
            # Member project statistics
            member_stats = TcdAppProject.objects.values('app_member_id').annotate(
                project_count=Count('id'),
                total_views=Sum('view'),
                avg_views=Avg('view'),
                completed_projects=Count('id', filter=Q(is_complete=True))
            ).order_by('-project_count')[:10]
            
            top_members = []
            for stat in member_stats:
                member = TcdAppMember.objects.filter(id=stat['app_member_id']).first()
                if member:
                    top_members.append({
                        'member_id': member.id,
                        'member_name': member.name,
                        'project_count': stat['project_count'],
                        'completed_projects': stat['completed_projects'],
                        'total_views': stat['total_views'] or 0,
                        'average_views': round(stat['avg_views'] or 0, 2)
                    })
            
            # Member type distribution
            member_types = TcdAppMember.objects.values('app_mas_member_type__name_th').annotate(
                count=Count('id')
            ).order_by('app_mas_member_type__name_th')
            
            # Recent member registrations
            thirty_days_ago = timezone.now() - timedelta(days=30)
            recent_members = TcdAppMember.objects.filter(
                create_date__gte=thirty_days_ago
            ).count()
            
            # Member engagement levels
            engagement_levels = [
                ('High', TcdAppMember.objects.filter(
                    id__in=TcdAppProject.objects.values('app_member_id').annotate(
                        project_count=Count('id')
                    ).filter(project_count__gte=5).values_list('app_member_id', flat=True)
                ).count()),
                ('Medium', TcdAppMember.objects.filter(
                    id__in=TcdAppProject.objects.values('app_member_id').annotate(
                        project_count=Count('id')
                    ).filter(project_count__gte=2, project_count__lt=5).values_list('app_member_id', flat=True)
                ).count()),
                ('Low', TcdAppMember.objects.filter(
                    id__in=TcdAppProject.objects.values('app_member_id').annotate(
                        project_count=Count('id')
                    ).filter(project_count=1).values_list('app_member_id', flat=True)
                ).count())
            ]
            
            data = {
                'top_members': top_members,
                'member_types': list(member_types),
                'recent_registrations': recent_members,
                'total_members': TcdAppMember.objects.count(),
                'active_members': TcdAppMember.objects.filter(status='Y').count(),
                'engagement_levels': [
                    {'level': level, 'count': count} for level, count in engagement_levels
                ]
            }
            
            return service_success_response(data, language)
            
        except Exception as e:
            logger.error(f"Error getting member analytics: {str(e)}")
            return service_error_response(5000, language)

    @staticmethod
    def get_system_health(language='th'):
        """
        Get system health and performance metrics
        
        Args:
            language (str): Language preference
            
        Returns:
            dict: Service response with system health data
        """
        try:
            # Database health checks
            now = timezone.now()
            
            # Recent activity indicators
            recent_logins = TcdAppLogs.objects.filter(
                action_date__gte=now - timedelta(hours=24)
            ).count()
            
            # Check for recent notifications
            recent_notifications = TcdAppNotification.objects.filter(
                create_date__gte=now - timedelta(hours=24)
            ).count()
            
            # Check for recent chat messages
            recent_messages = TcdChat.objects.filter(
                date__gte=now - timedelta(hours=24)
            ).count()
            
            # Data integrity checks
            projects_without_members = TcdAppProject.objects.filter(
                app_member__isnull=True
            ).count()
            
            consultants_without_projects = TcdUserConsult.objects.exclude(
                id__in=TcdAppProjectConsult.objects.values_list('user_consult_id', flat=True)
            ).count()
            
            # System utilization
            total_storage_used = TcdAppProject.objects.exclude(ref__isnull=True).exclude(ref='').count()
            
            data = {
                'activity_indicators': {
                    'recent_logins_24h': recent_logins,
                    'recent_notifications_24h': recent_notifications,
                    'recent_messages_24h': recent_messages
                },
                'data_integrity': {
                    'projects_without_members': projects_without_members,
                    'consultants_without_projects': consultants_without_projects,
                    'projects_with_documents': total_storage_used
                },
                'system_status': {
                    'status': 'healthy',
                    'last_check': now.isoformat(),
                    'uptime_indicator': 'operational'
                }
            }
            
            return service_success_response(data, language)
            
        except Exception as e:
            logger.error(f"Error getting system health: {str(e)}")
            return service_error_response(5000, language)

    @staticmethod
    def get_search_analytics(language='th'):
        """
        Get search and discovery analytics
        
        Args:
            language (str): Language preference
            
        Returns:
            dict: Service response with search analytics
        """
        try:
            # Most popular skills
            popular_skills = TcdAppProjectSkill.objects.values('skill_id').annotate(
                count=Count('app_project_id')
            ).order_by('-count')[:10]
            
            skill_stats = []
            for skill_stat in popular_skills:
                skill = TcdSkill.objects.filter(id=skill_stat['skill_id']).first()
                if skill:
                    skill_stats.append({
                        'skill_id': skill.id,
                        'skill_name': skill.name_th if language == 'th' else skill.name_en,
                        'project_count': skill_stat['count']
                    })
            
            # Most popular services
            popular_services = TcdAppProjectService.objects.values('service_id').annotate(
                count=Count('app_project_id')
            ).order_by('-count')[:10]
            
            service_stats = []
            for service_stat in popular_services:
                service = TcdService.objects.filter(id=service_stat['service_id']).first()
                if service:
                    service_stats.append({
                        'service_id': service.id,
                        'service_name': service.name_th if language == 'th' else service.name_en,
                        'project_count': service_stat['count']
                    })
            
            # Search trends (using project keywords as proxy)
            keyword_trends = TcdAppProject.objects.exclude(
                keyword__isnull=True
            ).exclude(
                keyword=''
            ).values('keyword').annotate(
                count=Count('id')
            ).order_by('-count')[:10]
            
            data = {
                'popular_skills': skill_stats,
                'popular_services': service_stats,
                'keyword_trends': list(keyword_trends),
                'total_sectors': TcdSector.objects.count(),
                'total_skills': TcdSkill.objects.count(),
                'total_services': TcdService.objects.count()
            }
            
            return service_success_response(data, language)
            
        except Exception as e:
            logger.error(f"Error getting search analytics: {str(e)}")
            return service_error_response(5000, language)


        """
        Generate a comprehensive summary report
        
        Args:
            period (str): Period in days
            language (str): Language preference
            
        Returns:
            dict: Service response with summary report
        """
        try:
            # Get all analytics data
            overview_result = DashboardStatsService.get_overview_stats(language)
            project_result = DashboardStatsService.get_project_analytics(period, language)
            consultant_result = DashboardStatsService.get_consultant_analytics(language)
            member_result = DashboardStatsService.get_member_analytics(language)
            search_result = DashboardStatsService.get_search_analytics(language)
            
            # Check if all services succeeded
            if not all([
                overview_result['success'],
                project_result['success'],
                consultant_result['success'],
                member_result['success'],
                search_result['success']
            ]):
                return service_error_response(5000, language)
            
            # Combine all data
            report_data = {
                'report_generated': timezone.now().isoformat(),
                'period_days': int(period),
                'overview': overview_result['data'],
                'projects': project_result['data'],
                'consultants': consultant_result['data'],
                'members': member_result['data'],
                'search_trends': search_result['data']
            }
            
            return service_success_response(report_data, language)
            
        except Exception as e:
            logger.error(f"Error generating summary report: {str(e)}")
            return service_error_response(5000, language) 

    @staticmethod
    def get_dashboard_category(language='th'):
        """
        Get dashboard category
        """
        try:
            result = TcdDashboardCategory.objects.filter(status=True).order_by('order')
            data = TcdDashboardCategorySerializer(result, many=True).data
            return service_success_response(data, language)
        except Exception as e:
            logger.error(f"Error getting dashboard category: {str(e)}")
            return service_error_response(5000, language)

    @staticmethod
    def get_dashboard_list(dashboard_category_id=None, language='th', page=1, page_size=10):
        """
        Get dashboard list with pagination
        """
        try:
            # Build queryset
            if dashboard_category_id:
                queryset = TcdDashboard.objects.filter(dashboard_category_id=dashboard_category_id, status=True, is_application=True).order_by('-id')
            else:
                queryset = TcdDashboard.objects.filter(status=True, is_application=True).order_by('-id')
            
            # Calculate pagination
            total = queryset.count()
            start = (page - 1) * page_size
            end = start + page_size
            
            # Get paginated data
            paginated_queryset = queryset[start:end]
            data = TcdDashboardSerializer(paginated_queryset, many=True).data
            
            # Process thumbnail URLs
            if data:
                base_file_url = getattr(settings, 'BASE_FILE_URL', '')
                dashboard_sub_dir = getattr(settings, 'DASHBOARD_SUB_DIR', '')
                dashboard_file_url = f"{base_file_url}{dashboard_sub_dir}"
                for dashboard in data:
                    dashboard['thumbnail'] = f"{dashboard_file_url}{dashboard['thumbnail']}" if dashboard['thumbnail'] else ''
            
            # Create paginated response
            response_data = {
                'success': True,
                'error_code': None,
                'error_message': None,
                'data': data,
                'page': page,
                'per_page': page_size,
                'total': total,
                'has_next': end < total,
                'api_version': 'v.0.0.1'
            }
            
            return response_data
        except Exception as e:
            logger.error(f"Error getting dashboard list: {str(e)}")
            return service_error_response(5000, language)

