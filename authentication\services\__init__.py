"""
OTP Service Package
"""

# Import OTP service
from .otp_service import OTPService

# Import utility functions from utils.response
from utils.response import service_success_response, service_error_response

# Import from the original services.py file to create a compatibility layer
import os
import importlib.util

# Get the absolute path to the services.py file
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
services_path = os.path.join(parent_dir, 'services.py')

# Load the services.py module using importlib
spec = importlib.util.spec_from_file_location('services_module', services_path)
services_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(services_module)

# Import the classes from the services module
MemberAuthService = services_module.MemberAuthService
ConsultantAuthService = services_module.ConsultantAuthService
ActionLogService = services_module.ActionLogService
PasswordResetService = services_module.PasswordResetService
StaffAuthService = services_module.StaffAuthService
ChangePasswordService = services_module.ChangePasswordService
DeleteMemberService = services_module.DeleteMemberService
UpdateMemberInfoService = services_module.UpdateMemberInfoService
UpdateMemberLangService = services_module.UpdateMemberLangService
UpdateTokenAppService = services_module.UpdateTokenAppService
AppUsageTrackingService = services_module.AppUsageTrackingService

# Export the classes and functions
__all__ = [
    'OTPService',
    'MemberAuthService',
    'ConsultantAuthService',
    'ActionLogService',
    'PasswordResetService',
    'StaffAuthService',
    'ChangePasswordService',
    'DeleteMemberService',
    'UpdateMemberInfoService',
    'UpdateMemberLangService',
    'UpdateTokenAppService',
    'AppUsageTrackingService',
    'service_success_response',
    'service_error_response',
]