from django.db import models


class TcdAppMasMemberType(models.Model):
    name_th = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_app_mas_member_type'
        
        
class TcdAppMasGovernmentSector(models.Model):
    name_th = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_app_mas_government_sector'


class TcdAppMasMinistry(models.Model):
    app_mas_government_sector = models.ForeignKey(TcdAppMasGovernmentSector, on_delete=models.PROTECT)
    name_th = models.Char<PERSON>ield(max_length=255, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_app_mas_ministry'


class TcdAppMasDepartment(models.Model):
    app_mas_ministry = models.ForeignKey(TcdAppMasMinistry, on_delete=models.PROTECT)
    name_th = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_app_mas_department'


class TcdAppMember(models.Model):
    name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    first_name = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    last_name = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    email = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    phone = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    identity_card_no = models.CharField(max_length=20, db_collation='Thai_CI_AI', blank=True, null=True)
    src = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    fb_id = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    google_id = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    apple_id = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    username = models.CharField(unique=True, max_length=50, db_collation='Thai_CI_AI')
    password = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    app_mas_member_type = models.ForeignKey(TcdAppMasMemberType, on_delete=models.PROTECT, blank=True, null=True)
    app_mas_government_sector = models.ForeignKey(TcdAppMasGovernmentSector, on_delete=models.PROTECT, blank=True, null=True)
    app_mas_government_sector_other = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    app_mas_ministry = models.ForeignKey(TcdAppMasMinistry, on_delete=models.PROTECT, blank=True, null=True)
    app_mas_ministry_other = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    app_mas_department = models.ForeignKey(TcdAppMasDepartment, on_delete=models.PROTECT, blank=True, null=True)
    app_mas_department_other = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    website = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    create_date = models.DateTimeField()
    token_app = models.CharField(max_length=250, db_collation='Thai_CI_AI', blank=True, null=True)
    is_notification = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    checklogin = models.IntegerField()
    lockout_end_date = models.DateTimeField(blank=True, null=True)
    status = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    lang = models.CharField(max_length=2, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_app_member'


class TcdAppMemberDelete(models.Model):
    app_member = models.ForeignKey(TcdAppMember, on_delete=models.PROTECT)
    first_name = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    last_name = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    email = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    phone = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    identity_card_no = models.CharField(max_length=20, db_collation='Thai_CI_AI', blank=True, null=True)
    delete_date = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'tcd_app_member_delete'


class TcdUserConsult(models.Model):
    consult_type = models.IntegerField(blank=True, null=True)
    corporate_type_id = models.DecimalField(max_digits=19, decimal_places=0, blank=True, null=True)
    password = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    username = models.CharField(unique=True, max_length=50, db_collation='Thai_CI_AI')
    email = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    email_second = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    phone = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    phone_second = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    src = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    maker_name = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    maker_phone = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    maker_email = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    pw = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    verify = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    reset_phone = models.IntegerField(blank=True, null=True)
    reset_phone_date = models.DateTimeField(blank=True, null=True)
    token_app = models.CharField(max_length=250, db_collation='Thai_CI_AI', blank=True, null=True)
    is_notification = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    score = models.FloatField()
    lang = models.CharField(max_length=2, db_collation='Thai_CI_AI')
    is_active_matching = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'tcd_user_consult'


class TcdUserConsultTeam(models.Model):
    user_consult = models.ForeignKey(TcdUserConsult, on_delete=models.PROTECT)
    first_name = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    last_name = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    identity_card_no = models.CharField(unique=True, max_length=13, db_collation='Thai_CI_AI')
    is_admin = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'tcd_user_consult_team'


class TcdActionLog(models.Model):
    action_date = models.DateTimeField()
    action_log = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    remark = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    user_name = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    consult_name = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    ip_address = models.CharField(max_length=255, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_action_log'


class TcdAppLogs(models.Model):
    action_date = models.DateTimeField()
    action_log = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    remark = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    ip_address = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    user_consult_id = models.DecimalField(max_digits=18, decimal_places=0, blank=True, null=True)
    app_member_id = models.DecimalField(max_digits=18, decimal_places=0, blank=True, null=True)
    name = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    type = models.CharField(max_length=15, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_app_logs'


class TcdStatisticExternalLogin(models.Model):
    type = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    login_provider = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    ip = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    date = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'tcd_statistic_external_login'


class TcdAppStatistic(models.Model):
    type = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    ip = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    date = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'tcd_app_statistic'

